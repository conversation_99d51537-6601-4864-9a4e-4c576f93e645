
#!/bin/bash

# Laravel Development Environment Setup Script for Arch Linux
# Enhanced with visual feedback and comprehensive Laravel/Filament support
# Author: Enhanced by AI Assistant
# Version: 2.0

set -e  # Exit on any error

# Color codes for visual feedback
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
NC='\033[0m' # No Color

# Unicode symbols for better visual feedback
CHECK="✓"
CROSS="✗"
ARROW="→"
STAR="★"
GEAR="⚙"
ROCKET="🚀"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[${CHECK}]${NC} $1"
}

print_error() {
    echo -e "${RED}[${CROSS}]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[!]${NC} $1"
}

print_header() {
    echo -e "\n${PURPLE}${STAR}${STAR}${STAR} $1 ${STAR}${STAR}${STAR}${NC}\n"
}

print_step() {
    echo -e "${CYAN}${ARROW}${NC} $1"
}

# Function to show progress bar
show_progress() {
    local duration=$1
    local message=$2
    echo -ne "${YELLOW}${GEAR} ${message}${NC}"
    for ((i=0; i<=duration; i++)); do
        echo -ne "."
        sleep 0.1
    done
    echo -e " ${GREEN}Done!${NC}"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check if package is installed
package_installed() {
    pacman -Qi "$1" >/dev/null 2>&1
}

# Welcome banner
clear
echo -e "${PURPLE}"
cat << "EOF"
╔══════════════════════════════════════════════════════════════════════════════╗
║                                                                              ║
║    ██╗      █████╗ ██████╗  █████╗ ██╗   ██╗███████╗██╗                     ║
║    ██║     ██╔══██╗██╔══██╗██╔══██╗██║   ██║██╔════╝██║                     ║
║    ██║     ███████║██████╔╝███████║██║   ██║█████╗  ██║                     ║
║    ██║     ██╔══██║██╔══██╗██╔══██║╚██╗ ██╔╝██╔══╝  ██║                     ║
║    ███████╗██║  ██║██║  ██║██║  ██║ ╚████╔╝ ███████╗███████╗                ║
║    ╚══════╝╚═╝  ╚═╝╚═╝  ╚═╝╚═╝  ╚═╝  ╚═══╝  ╚══════╝╚══════╝                ║
║                                                                              ║
║              DEVELOPMENT ENVIRONMENT SETUP FOR ARCH LINUX                   ║
║                     Enhanced with Filament Support                          ║
║                                                                              ║
╚══════════════════════════════════════════════════════════════════════════════╝
EOF
echo -e "${NC}"

echo -e "${WHITE}Welcome to the enhanced Laravel Development Environment Setup!${NC}"
echo -e "${CYAN}This script will install and configure everything you need for Laravel and Filament development.${NC}\n"

# Confirmation prompt
read -p "$(echo -e ${YELLOW}Do you want to continue? [y/N]: ${NC})" -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    print_error "Setup cancelled by user."
    exit 1
fi

print_header "SYSTEM UPDATE"
print_step "Updating system packages..."
show_progress 10 "Syncing package databases"
sudo pacman -Syu --noconfirm

print_header "INSTALLING CORE PACKAGES"

# Core packages needed for Laravel and Filament
PACKAGES=(
    "php"
    "php-fpm"
    "php-apache"
    "composer"
    "nodejs"
    "npm"
    "git"
    "curl"
    "wget"
    "unzip"
    "postgresql"
    "redis"
    "nginx"
    "supervisor"
)

# PHP extensions required for Laravel and Filament
PHP_EXTENSIONS=(
    "php-bcmath"
    "php-ctype"
    "php-curl"
    "php-dom"
    "php-fileinfo"
    "php-gd"
    "php-intl"
    "php-json"
    "php-mbstring"
    "php-openssl"
    "php-pdo"
    "php-pgsql"
    "php-sqlite"
    "php-tokenizer"
    "php-xml"
    "php-zip"
    "php-redis"
    "php-imagick"
    "php-exif"
    "php-iconv"
    "php-simplexml"
    "php-xmlreader"
    "php-zlib"
)

print_step "Installing core packages..."
for package in "${PACKAGES[@]}"; do
    if package_installed "$package"; then
        print_success "$package is already installed"
    else
        print_step "Installing $package..."
        sudo pacman -S --noconfirm "$package"
        print_success "$package installed successfully"
    fi
done

print_step "Installing PHP extensions..."
for extension in "${PHP_EXTENSIONS[@]}"; do
    if package_installed "$extension"; then
        print_success "$extension is already installed"
    else
        print_step "Installing $extension..."
        sudo pacman -S --noconfirm "$extension" 2>/dev/null || print_warning "$extension not available in repos"
    fi
done

print_header "CONFIGURING PHP"

# Find PHP configuration file
PHP_INI=$(php --ini | grep "Loaded Configuration File" | cut -d: -f2 | xargs)
if [[ -z "$PHP_INI" ]]; then
    PHP_INI="/etc/php/php.ini"
fi

print_step "PHP configuration file: $PHP_INI"

# Backup original php.ini
if [[ ! -f "$PHP_INI.backup" ]]; then
    print_step "Creating backup of php.ini..."
    sudo cp "$PHP_INI" "$PHP_INI.backup"
    print_success "Backup created: $PHP_INI.backup"
fi

print_step "Configuring PHP extensions..."

# Function to enable PHP extension
enable_php_extension() {
    local extension=$1
    local ini_file=$2

    if grep -q "^extension=${extension}" "$ini_file"; then
        print_success "Extension $extension is already enabled"
    elif grep -q "^;extension=${extension}" "$ini_file"; then
        print_step "Enabling extension: $extension"
        sudo sed -i "s/^;extension=${extension}/extension=${extension}/" "$ini_file"
        print_success "Extension $extension enabled"
    else
        print_step "Adding extension: $extension"
        echo "extension=${extension}" | sudo tee -a "$ini_file" > /dev/null
        print_success "Extension $extension added"
    fi
}

# Enable required extensions
REQUIRED_EXTENSIONS=(
    "bcmath"
    "ctype"
    "curl"
    "dom"
    "fileinfo"
    "gd"
    "intl"
    "json"
    "mbstring"
    "openssl"
    "pdo_mysql"
    "pdo_pgsql"
    "pdo_sqlite"
    "pgsql"
    "redis"
    "sqlite3"
    "tokenizer"
    "xml"
    "zip"
    "imagick"
    "exif"
    "iconv"
    "simplexml"
    "xmlreader"
    "zlib"
)

for ext in "${REQUIRED_EXTENSIONS[@]}"; do
    enable_php_extension "$ext" "$PHP_INI"
done

# Configure PHP settings for Laravel/Filament
print_step "Optimizing PHP settings for Laravel/Filament..."

# Function to set PHP configuration
set_php_config() {
    local setting=$1
    local value=$2
    local ini_file=$3

    if grep -q "^${setting}" "$ini_file"; then
        sudo sed -i "s/^${setting}.*/${setting} = ${value}/" "$ini_file"
    elif grep -q "^;${setting}" "$ini_file"; then
        sudo sed -i "s/^;${setting}.*/${setting} = ${value}/" "$ini_file"
    else
        echo "${setting} = ${value}" | sudo tee -a "$ini_file" > /dev/null
    fi
    print_success "Set ${setting} = ${value}"
}

# Optimize PHP settings
set_php_config "memory_limit" "512M" "$PHP_INI"
set_php_config "upload_max_filesize" "100M" "$PHP_INI"
set_php_config "post_max_size" "100M" "$PHP_INI"
set_php_config "max_execution_time" "300" "$PHP_INI"
set_php_config "max_input_vars" "3000" "$PHP_INI"
set_php_config "date.timezone" "UTC" "$PHP_INI"

print_header "INSTALLING COMPOSER PACKAGES"

# Install Laravel installer globally
if command_exists composer; then
    print_step "Installing Laravel installer..."
    composer global require laravel/installer
    print_success "Laravel installer installed globally"

    # Add composer global bin to PATH
    COMPOSER_PATH="$HOME/.config/composer/vendor/bin"
    if [[ ":$PATH:" != *":$COMPOSER_PATH:"* ]]; then
        echo "export PATH=\"\$PATH:$COMPOSER_PATH\"" >> ~/.bashrc
        export PATH="$PATH:$COMPOSER_PATH"
        print_success "Added Composer global bin to PATH"
    fi
else
    print_error "Composer not found. Please install Composer first."
fi

print_header "CONFIGURING DATABASES"

# PostgreSQL setup
print_step "Configuring PostgreSQL..."
if systemctl is-active --quiet postgresql; then
    print_success "PostgreSQL is already running"
else
    print_step "Initializing PostgreSQL database..."
    sudo -u postgres initdb -D /var/lib/postgres/data

    print_step "Starting PostgreSQL service..."
    sudo systemctl start postgresql
    sudo systemctl enable postgresql
    print_success "PostgreSQL started and enabled"
fi

# Create Laravel database user
print_step "Creating Laravel database user..."
sudo -u postgres psql -c "CREATE USER laravel WITH PASSWORD 'laravel';" 2>/dev/null || print_warning "User 'laravel' may already exist"
sudo -u postgres psql -c "ALTER USER laravel CREATEDB;" 2>/dev/null
sudo -u postgres psql -c "CREATE DATABASE laravel OWNER laravel;" 2>/dev/null || print_warning "Database 'laravel' may already exist"
print_success "PostgreSQL user 'laravel' configured"

# Redis setup
print_step "Configuring Redis..."
sudo systemctl start redis
sudo systemctl enable redis
print_success "Redis started and enabled"

print_header "CONFIGURING NODE.JS & NPM"

# Install global npm packages for Laravel development
print_step "Installing global npm packages..."
NPM_PACKAGES=(
    "@vue/cli"
    "vite"
    "@vitejs/plugin-vue"
    "cross-env"
)

for package in "${NPM_PACKAGES[@]}"; do
    print_step "Installing $package..."
    npm install -g "$package"
    print_success "$package installed globally"
done

print_header "CONFIGURING NGINX"

# Basic Nginx configuration for Laravel
print_step "Configuring Nginx for Laravel development..."

# Create Laravel site configuration
NGINX_SITE_CONFIG="/etc/nginx/sites-available/laravel-dev"
sudo mkdir -p /etc/nginx/sites-available /etc/nginx/sites-enabled

sudo tee "$NGINX_SITE_CONFIG" > /dev/null << 'EOF'
server {
    listen 80;
    server_name localhost laravel.local;
    root /var/www/laravel/public;
    index index.php index.html index.htm;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location ~ \.php$ {
        fastcgi_pass unix:/run/php-fpm/php-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
    }

    location ~ /\.ht {
        deny all;
    }

    # Handle static assets
    location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
EOF

# Enable the site
sudo ln -sf "$NGINX_SITE_CONFIG" /etc/nginx/sites-enabled/
print_success "Nginx Laravel configuration created"

# Test Nginx configuration
if sudo nginx -t; then
    print_success "Nginx configuration is valid"
    sudo systemctl enable nginx
    print_success "Nginx enabled for startup"
else
    print_error "Nginx configuration has errors"
fi

print_header "CONFIGURING PHP-FPM"

# Configure PHP-FPM
print_step "Configuring PHP-FPM..."
sudo systemctl start php-fpm
sudo systemctl enable php-fpm
print_success "PHP-FPM started and enabled"

print_header "VERIFICATION & TESTING"

# Verify installations
print_step "Verifying installations..."
echo ""

# Check PHP
if command_exists php; then
    PHP_VERSION=$(php --version | head -n1)
    print_success "PHP: $PHP_VERSION"
else
    print_error "PHP installation failed"
fi

# Check Composer
if command_exists composer; then
    COMPOSER_VERSION=$(composer --version | head -n1)
    print_success "Composer: $COMPOSER_VERSION"
else
    print_error "Composer installation failed"
fi

# Check Node.js
if command_exists node; then
    NODE_VERSION=$(node --version)
    print_success "Node.js: $NODE_VERSION"
else
    print_error "Node.js installation failed"
fi

# Check NPM
if command_exists npm; then
    NPM_VERSION=$(npm --version)
    print_success "NPM: v$NPM_VERSION"
else
    print_error "NPM installation failed"
fi

# Check PostgreSQL
if command_exists psql; then
    PSQL_VERSION=$(psql --version)
    print_success "PostgreSQL: $PSQL_VERSION"
else
    print_error "PostgreSQL installation failed"
fi

# Check Redis
if command_exists redis-server; then
    REDIS_VERSION=$(redis-server --version | head -n1)
    print_success "Redis: $REDIS_VERSION"
else
    print_error "Redis installation failed"
fi

# Check Laravel installer
if command_exists laravel; then
    LARAVEL_VERSION=$(laravel --version)
    print_success "Laravel Installer: $LARAVEL_VERSION"
else
    print_warning "Laravel installer not found in PATH. You may need to source ~/.bashrc"
fi

echo ""
print_step "Checking enabled PHP extensions..."
ENABLED_EXTENSIONS=$(php -m | grep -E "(bcmath|ctype|curl|dom|fileinfo|gd|intl|json|mbstring|openssl|pdo|pgsql|redis|sqlite3|tokenizer|xml|zip|imagick|exif)" | sort)

if [[ -n "$ENABLED_EXTENSIONS" ]]; then
    print_success "Enabled PHP extensions:"
    echo -e "${GREEN}$ENABLED_EXTENSIONS${NC}"
else
    print_error "No required PHP extensions found"
fi

# Check services status
echo ""
print_step "Checking service status..."

services=("postgresql" "redis" "php-fpm")
for service in "${services[@]}"; do
    if systemctl is-active --quiet "$service"; then
        print_success "$service is running"
    else
        print_error "$service is not running"
    fi
done

print_header "SETUP COMPLETED SUCCESSFULLY!"

echo -e "${GREEN}"
cat << "EOF"
╔══════════════════════════════════════════════════════════════════════════════╗
║                                                                              ║
║    🎉 CONGRATULATIONS! Your Laravel development environment is ready! 🎉     ║
║                                                                              ║
╚══════════════════════════════════════════════════════════════════════════════╝
EOF
echo -e "${NC}"

echo -e "${WHITE}${ROCKET} NEXT STEPS:${NC}"
echo ""
echo -e "${CYAN}1.${NC} ${YELLOW}Source your bashrc to update PATH:${NC}"
echo -e "   ${BLUE}source ~/.bashrc${NC}"
echo ""
echo -e "${CYAN}2.${NC} ${YELLOW}Create a new Laravel project:${NC}"
echo -e "   ${BLUE}laravel new my-awesome-project${NC}"
echo -e "   ${GRAY}or${NC}"
echo -e "   ${BLUE}composer create-project laravel/laravel my-awesome-project${NC}"
echo ""
echo -e "${CYAN}3.${NC} ${YELLOW}Install Filament (optional):${NC}"
echo -e "   ${BLUE}cd my-awesome-project${NC}"
echo -e "   ${BLUE}composer require filament/filament${NC}"
echo -e "   ${BLUE}php artisan filament:install --panels${NC}"
echo ""
echo -e "${CYAN}4.${NC} ${YELLOW}Configure your .env file with database settings:${NC}"
echo -e "   ${BLUE}DB_CONNECTION=pgsql${NC}"
echo -e "   ${BLUE}DB_HOST=127.0.0.1${NC}"
echo -e "   ${BLUE}DB_PORT=5432${NC}"
echo -e "   ${BLUE}DB_DATABASE=laravel${NC}"
echo -e "   ${BLUE}DB_USERNAME=laravel${NC}"
echo -e "   ${BLUE}DB_PASSWORD=laravel${NC}"
echo ""
echo -e "${CYAN}5.${NC} ${YELLOW}Start the development server:${NC}"
echo -e "   ${BLUE}php artisan serve${NC}"
echo ""

echo -e "${WHITE}${GEAR} USEFUL INFORMATION:${NC}"
echo ""
echo -e "${YELLOW}Database Access:${NC}"
echo -e "  • PostgreSQL: ${BLUE}sudo -u postgres psql${NC}"
echo -e "  • Laravel DB: ${BLUE}psql -h localhost -U laravel -d laravel${NC}"
echo ""
echo -e "${YELLOW}Services Management:${NC}"
echo -e "  • PostgreSQL: ${BLUE}sudo systemctl [start|stop|restart] postgresql${NC}"
echo -e "  • Redis: ${BLUE}sudo systemctl [start|stop|restart] redis${NC}"
echo -e "  • PHP-FPM: ${BLUE}sudo systemctl [start|stop|restart] php-fpm${NC}"
echo -e "  • Nginx: ${BLUE}sudo systemctl [start|stop|restart] nginx${NC}"
echo ""
echo -e "${YELLOW}Configuration Files:${NC}"
echo -e "  • PHP: ${BLUE}$PHP_INI${NC}"
echo -e "  • PHP Backup: ${BLUE}$PHP_INI.backup${NC}"
echo -e "  • Nginx Laravel: ${BLUE}/etc/nginx/sites-available/laravel-dev${NC}"
echo ""

echo -e "${GREEN}${CHECK} All components installed and configured successfully!${NC}"
echo -e "${GREEN}${CHECK} PHP extensions enabled and optimized for Laravel/Filament${NC}"
echo -e "${GREEN}${CHECK} Database and caching services ready${NC}"
echo -e "${GREEN}${CHECK} Development tools and global packages installed${NC}"
echo ""
echo -e "${PURPLE}Happy coding with Laravel and Filament! ${ROCKET}${NC}"
