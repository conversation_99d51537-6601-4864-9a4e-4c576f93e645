#!/bin/bash

# Redis Installation and Startup Script for Arch Linux
# This script ensures Redis is installed and running

# Color codes
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[✓]${NC} $1"
}

print_error() {
    echo -e "${RED}[✗]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[!]${NC} $1"
}

print_step() {
    echo -e "${BLUE}→${NC} $1"
}

echo -e "${BLUE}Redis Installation and Startup Script${NC}"
echo "====================================="

# Function to check if package is installed
package_installed() {
    pacman -Qi "$1" >/dev/null 2>&1
}

# Function to install Redis
install_redis() {
    print_step "Installing Redis..."
    if sudo pacman -S --noconfirm redis; then
        print_success "Redis package installed"
        return 0
    else
        print_error "Failed to install Redis from official repositories"
        
        # Try AUR helpers as fallback
        if command -v yay >/dev/null 2>&1; then
            print_step "Trying to install Redis with yay..."
            if yay -S --noconfirm redis; then
                print_success "Redis installed with yay"
                return 0
            fi
        elif command -v paru >/dev/null 2>&1; then
            print_step "Trying to install Redis with paru..."
            if paru -S --noconfirm redis; then
                print_success "Redis installed with paru"
                return 0
            fi
        fi
        
        print_error "Failed to install Redis"
        return 1
    fi
}

# Function to create Redis systemd service
create_redis_service() {
    print_step "Creating Redis systemd service..."
    
    sudo tee /etc/systemd/system/redis.service > /dev/null << 'EOF'
[Unit]
Description=Redis In-Memory Data Store
After=network.target

[Service]
User=redis
Group=redis
ExecStart=/usr/bin/redis-server /etc/redis/redis.conf
ExecStop=/usr/bin/redis-cli shutdown
Restart=always

[Install]
WantedBy=multi-user.target
EOF
    
    # Create redis user if it doesn't exist
    if ! id redis &>/dev/null; then
        sudo useradd -r -s /bin/false redis
        print_success "Created redis user"
    fi
    
    # Create Redis configuration
    sudo mkdir -p /etc/redis
    if [[ ! -f /etc/redis/redis.conf ]]; then
        sudo tee /etc/redis/redis.conf > /dev/null << 'EOF'
bind 127.0.0.1
port 6379
timeout 0
save 900 1
save 300 10
save 60 10000
rdbcompression yes
dbfilename dump.rdb
dir /var/lib/redis/
logfile /var/log/redis.log
EOF
        print_success "Created Redis configuration"
    fi
    
    # Create data directory
    sudo mkdir -p /var/lib/redis
    sudo chown redis:redis /var/lib/redis
    
    sudo systemctl daemon-reload
    print_success "Redis systemd service created"
}

# Function to start Redis service
start_redis_service() {
    local service_name=""
    
    # Check for different Redis service names
    if systemctl list-unit-files | grep -q "^redis.service"; then
        service_name="redis"
    elif systemctl list-unit-files | grep -q "^redis-server.service"; then
        service_name="redis-server"
    else
        create_redis_service
        service_name="redis"
    fi
    
    # Start and enable the service
    print_step "Starting Redis service ($service_name)..."
    if sudo systemctl start "$service_name" 2>/dev/null; then
        sudo systemctl enable "$service_name"
        print_success "Redis service ($service_name) started and enabled"
        return 0
    else
        print_error "Failed to start Redis service ($service_name)"
        
        # Show service status for debugging
        print_status "Service status:"
        sudo systemctl status "$service_name" --no-pager -l
        return 1
    fi
}

# Main execution
print_status "Checking Redis installation status..."

# Check if Redis is installed
if ! package_installed "redis" && ! command -v redis-server >/dev/null 2>&1; then
    print_warning "Redis is not installed."
    
    if install_redis; then
        print_success "Redis installation completed"
    else
        print_error "Redis installation failed"
        exit 1
    fi
else
    print_success "Redis is already installed"
fi

# Check if Redis is running
print_status "Checking Redis service status..."

redis_running=false

if systemctl is-active --quiet redis 2>/dev/null; then
    print_success "Redis service is already running"
    redis_running=true
elif systemctl is-active --quiet redis-server 2>/dev/null; then
    print_success "Redis server service is already running"
    redis_running=true
elif pgrep redis-server >/dev/null 2>&1; then
    print_success "Redis server is running (not as systemd service)"
    redis_running=true
fi

# Start Redis if not running
if [[ "$redis_running" == false ]]; then
    print_step "Redis is not running. Starting Redis service..."
    if start_redis_service; then
        print_success "Redis started successfully"
    else
        print_error "Failed to start Redis service"
        exit 1
    fi
fi

# Test Redis connection
if command -v redis-cli >/dev/null 2>&1; then
    print_step "Testing Redis connection..."
    if timeout 5 redis-cli ping >/dev/null 2>&1; then
        print_success "Redis is responding to ping"
        
        # Show Redis info
        print_status "Redis server information:"
        redis-cli info server | grep -E "(redis_version|os|arch|process_id|tcp_port)" | head -5
    else
        print_warning "Redis is not responding (may take a moment to start)"
        print_status "Waiting 3 seconds and trying again..."
        sleep 3
        if timeout 5 redis-cli ping >/dev/null 2>&1; then
            print_success "Redis is now responding to ping"
        else
            print_error "Redis is still not responding"
        fi
    fi
else
    print_warning "redis-cli not found. Cannot test connection."
fi

print_success "Redis setup completed!"
echo ""
echo -e "${BLUE}Redis is now ready for Laravel development.${NC}"
echo -e "${YELLOW}You can test Redis with: redis-cli ping${NC}"
echo -e "${YELLOW}To stop Redis: sudo systemctl stop redis${NC}"
echo -e "${YELLOW}To restart Redis: sudo systemctl restart redis${NC}"
