#!/bin/bash

# Node.js and npm permission fix for Arch Linux
# This script fixes npm global package installation issues

# Color codes
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[✓]${NC} $1"
}

print_error() {
    echo -e "${RED}[✗]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[!]${NC} $1"
}

echo -e "${BLUE}Node.js & npm Permission Fix${NC}"
echo "============================="

# Check current Node.js version
if command -v node >/dev/null 2>&1; then
    NODE_VERSION=$(node --version)
    print_success "Node.js version: $NODE_VERSION"
else
    print_error "Node.js not found"
    exit 1
fi

# Check current npm version
if command -v npm >/dev/null 2>&1; then
    NPM_VERSION=$(npm --version)
    print_success "npm version: $NPM_VERSION"
else
    print_error "npm not found"
    exit 1
fi

# Configure npm to use user directory
print_status "Configuring npm for user-level global packages..."
NPM_CONFIG_PREFIX="$HOME/.npm-global"
mkdir -p "$NPM_CONFIG_PREFIX"
npm config set prefix "$NPM_CONFIG_PREFIX"
print_success "npm configured to use $NPM_CONFIG_PREFIX"

# Add to PATH if not already there
if [[ ":$PATH:" != *":$NPM_CONFIG_PREFIX/bin:"* ]]; then
    echo "export PATH=\"\$PATH:$NPM_CONFIG_PREFIX/bin\"" >> ~/.bashrc
    export PATH="$PATH:$NPM_CONFIG_PREFIX/bin"
    print_success "Added npm global bin to PATH"
else
    print_success "npm global bin already in PATH"
fi

# Install Bun.js if not present
print_status "Checking for Bun.js..."
if command -v bun >/dev/null 2>&1; then
    BUN_VERSION=$(bun --version)
    print_success "Bun.js already installed: v$BUN_VERSION"
elif [[ -f "$HOME/.bun/bin/bun" ]]; then
    BUN_VERSION=$($HOME/.bun/bin/bun --version)
    print_success "Bun.js found in ~/.bun/bin: v$BUN_VERSION"
else
    print_status "Installing Bun.js..."
    if curl -fsSL https://bun.sh/install | bash; then
        if [[ -f "$HOME/.bun/bin/bun" ]]; then
            if [[ ":$PATH:" != *":$HOME/.bun/bin:"* ]]; then
                echo 'export PATH="$HOME/.bun/bin:$PATH"' >> ~/.bashrc
                export PATH="$HOME/.bun/bin:$PATH"
                print_success "Added Bun to PATH"
            fi
            BUN_VERSION=$($HOME/.bun/bin/bun --version)
            print_success "Bun.js installed successfully: v$BUN_VERSION"
        else
            print_error "Bun installation failed"
        fi
    else
        print_error "Failed to download Bun.js installer"
    fi
fi

# Test npm global installation
print_status "Testing npm global package installation..."
if npm install -g cross-env >/dev/null 2>&1; then
    print_success "npm global installation working correctly"
    npm uninstall -g cross-env >/dev/null 2>&1
else
    print_error "npm global installation still has issues"
fi

# Install essential packages
print_status "Installing essential development packages..."

PACKAGES=(
    "vite"
    "cross-env"
    "typescript"
    "eslint"
    "prettier"
)

for package in "${PACKAGES[@]}"; do
    print_status "Installing $package..."
    if npm install -g "$package" >/dev/null 2>&1; then
        print_success "$package installed successfully"
    else
        print_warning "Failed to install $package"
    fi
done

# Show current configuration
echo ""
print_status "Current npm configuration:"
echo "npm prefix: $(npm config get prefix)"
echo "npm global packages location: $(npm root -g)"
echo ""

print_success "Node.js and npm configuration completed!"
echo ""
echo -e "${YELLOW}Important:${NC} Run 'source ~/.bashrc' to update your PATH"
echo -e "${YELLOW}Or restart your terminal session${NC}"
echo ""
echo -e "${BLUE}You can now install global packages with:${NC}"
echo -e "  npm install -g <package-name>"
echo ""
echo -e "${BLUE}Alternative with Bun.js (faster):${NC}"
echo -e "  bun add -g <package-name>"
