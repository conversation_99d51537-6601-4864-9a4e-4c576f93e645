#!/bin/bash

# Imagick Version Mismatch Fix for Arch Linux
# This script fixes the ImageMagick/Imagick version mismatch warning

# Color codes
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[✓]${NC} $1"
}

print_error() {
    echo -e "${RED}[✗]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[!]${NC} $1"
}

echo -e "${BLUE}Imagick Version Mismatch Fix${NC}"
echo "============================"

# Check current versions
print_status "Checking current versions..."

IMAGEMAGICK_VERSION=$(pacman -Q imagemagick | cut -d' ' -f2)
PHP_IMAGICK_VERSION=$(pacman -Q php-imagick | cut -d' ' -f2)

echo "ImageMagick library: $IMAGEMAGICK_VERSION"
echo "PHP Imagick extension: $PHP_IMAGICK_VERSION"

# Check if there's a version mismatch
if php -m 2>&1 | grep -q "Version warning: Imagick"; then
    print_warning "Version mismatch detected between Imagick extension and ImageMagick library"
    
    print_status "Attempting to fix the version mismatch..."
    
    # Method 1: Update php-imagick package
    print_step "Method 1: Updating php-imagick package..."
    if sudo pacman -Sy php-imagick --noconfirm; then
        print_success "php-imagick package updated"
        
        # Test if the warning is gone
        if php -m 2>&1 | grep -q "Version warning: Imagick"; then
            print_warning "Warning still present after package update"
        else
            print_success "Version mismatch resolved!"
            exit 0
        fi
    else
        print_warning "Failed to update php-imagick package"
    fi
    
    # Method 2: Rebuild php-imagick from AUR if available
    print_step "Method 2: Checking for AUR helpers to rebuild php-imagick..."
    
    if command -v yay >/dev/null 2>&1; then
        print_status "Found yay. Attempting to rebuild php-imagick..."
        if yay -S php-imagick --noconfirm --rebuild; then
            print_success "php-imagick rebuilt with yay"
            
            # Test again
            if php -m 2>&1 | grep -q "Version warning: Imagick"; then
                print_warning "Warning still present after rebuild"
            else
                print_success "Version mismatch resolved!"
                exit 0
            fi
        else
            print_warning "Failed to rebuild with yay"
        fi
    elif command -v paru >/dev/null 2>&1; then
        print_status "Found paru. Attempting to rebuild php-imagick..."
        if paru -S php-imagick --noconfirm --rebuild; then
            print_success "php-imagick rebuilt with paru"
            
            # Test again
            if php -m 2>&1 | grep -q "Version warning: Imagick"; then
                print_warning "Warning still present after rebuild"
            else
                print_success "Version mismatch resolved!"
                exit 0
            fi
        else
            print_warning "Failed to rebuild with paru"
        fi
    else
        print_warning "No AUR helper found (yay or paru)"
    fi
    
    # Method 3: Temporarily disable the warning (not recommended but functional)
    print_step "Method 3: Checking if Imagick is actually working despite the warning..."
    
    # Test if Imagick actually works
    if php -r "
    try {
        \$imagick = new Imagick();
        echo 'Imagick is functional' . PHP_EOL;
        exit(0);
    } catch (Exception \$e) {
        echo 'Imagick error: ' . \$e->getMessage() . PHP_EOL;
        exit(1);
    }
    " 2>/dev/null; then
        print_success "Imagick extension is functional despite the version warning"
        
        echo ""
        print_status "Options to handle this warning:"
        echo ""
        echo -e "${YELLOW}Option 1 (Recommended):${NC} Wait for php-imagick package update"
        echo -e "  The warning is harmless and Imagick works correctly."
        echo -e "  The package maintainers will update php-imagick soon."
        echo ""
        echo -e "${YELLOW}Option 2:${NC} Suppress the warning in php.ini"
        echo -e "  Add 'error_reporting = E_ALL & ~E_WARNING' to php.ini"
        echo -e "  This hides the warning but doesn't fix the underlying issue."
        echo ""
        echo -e "${YELLOW}Option 3:${NC} Remove Imagick if not needed"
        echo -e "  Run: sudo pacman -R php-imagick"
        echo -e "  Only do this if you don't need image processing in PHP."
        echo ""
        
        read -p "$(echo -e ${BLUE}Do you want to suppress the warning in php.ini? [y/N]: ${NC})" -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            # Find php.ini
            PHP_INI=$(php --ini 2>/dev/null | grep "Loaded Configuration File" | cut -d: -f2 | xargs)
            if [[ -z "$PHP_INI" ]]; then
                PHP_INI="/etc/php/php.ini"
            fi
            
            print_step "Suppressing Imagick version warnings in $PHP_INI..."
            
            # Backup php.ini if not already backed up
            if [[ ! -f "$PHP_INI.backup-imagick" ]]; then
                sudo cp "$PHP_INI" "$PHP_INI.backup-imagick"
                print_success "Created backup: $PHP_INI.backup-imagick"
            fi
            
            # Add or modify error_reporting to exclude warnings
            if grep -q "^error_reporting" "$PHP_INI"; then
                print_step "Modifying existing error_reporting setting..."
                sudo sed -i 's/^error_reporting.*/error_reporting = E_ALL \& ~E_WARNING/' "$PHP_INI"
            else
                print_step "Adding error_reporting setting..."
                echo "error_reporting = E_ALL & ~E_WARNING" | sudo tee -a "$PHP_INI" > /dev/null
            fi
            
            print_success "Warning suppression configured"
            print_warning "Note: This hides ALL PHP warnings, not just Imagick warnings"
            
            # Test
            if php -m 2>&1 | grep -q "Version warning: Imagick"; then
                print_warning "Warning still visible. You may need to restart PHP-FPM:"
                echo "sudo systemctl restart php-fpm"
            else
                print_success "Imagick warning suppressed"
            fi
        else
            print_status "No changes made. The warning is harmless and can be ignored."
        fi
    else
        print_error "Imagick extension is not working properly"
        print_status "Attempting to reinstall php-imagick..."
        sudo pacman -R php-imagick --noconfirm
        sudo pacman -S php-imagick --noconfirm
        print_success "php-imagick reinstalled"
    fi
    
else
    print_success "No Imagick version mismatch detected"
fi

echo ""
print_status "Current status:"
if php -m 2>&1 | grep -q "Version warning: Imagick"; then
    print_warning "Imagick version warning is still present"
    echo -e "${YELLOW}This warning is typically harmless and doesn't affect functionality.${NC}"
    echo -e "${BLUE}Filament and Laravel will work correctly despite this warning.${NC}"
else
    print_success "No Imagick warnings detected"
fi

echo ""
print_success "Imagick fix process completed!"
echo -e "${BLUE}Your Laravel/Filament applications should work normally.${NC}"
