#!/bin/bash

# Quick Redis fix for Arch Linux
# This script addresses the Redis service issue

# Color codes
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[✓]${NC} $1"
}

print_error() {
    echo -e "${RED}[✗]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[!]${NC} $1"
}

echo -e "${BLUE}Redis Quick Fix for Arch Linux${NC}"
echo "=================================="

# Check if Redis is installed
if pacman -Qi redis >/dev/null 2>&1; then
    print_success "Redis package is installed"
else
    print_warning "Redis package not found. Installing..."
    sudo pacman -S --noconfirm redis
fi

# Check for Redis binary
if command -v redis-server >/dev/null 2>&1; then
    print_success "Redis server binary found"
else
    print_error "Redis server binary not found"
    exit 1
fi

# Check systemd service files
print_status "Checking Redis systemd services..."

if systemctl list-unit-files | grep -q "redis.service"; then
    print_success "redis.service found"
    SERVICE_NAME="redis"
elif systemctl list-unit-files | grep -q "redis-server.service"; then
    print_success "redis-server.service found"
    SERVICE_NAME="redis-server"
else
    print_warning "No Redis systemd service found. Creating one..."
    
    # Create systemd service
    sudo tee /etc/systemd/system/redis.service > /dev/null << 'EOF'
[Unit]
Description=Redis In-Memory Data Store
After=network.target

[Service]
User=redis
Group=redis
ExecStart=/usr/bin/redis-server /etc/redis/redis.conf
ExecStop=/usr/bin/redis-cli shutdown
Restart=always

[Install]
WantedBy=multi-user.target
EOF
    
    # Create redis user
    if ! id redis &>/dev/null; then
        sudo useradd -r -s /bin/false redis
        print_success "Created redis user"
    fi
    
    # Create config directory
    sudo mkdir -p /etc/redis
    
    # Create basic config if it doesn't exist
    if [[ ! -f /etc/redis/redis.conf ]]; then
        sudo tee /etc/redis/redis.conf > /dev/null << 'EOF'
bind 127.0.0.1
port 6379
timeout 0
save 900 1
save 300 10
save 60 10000
rdbcompression yes
dbfilename dump.rdb
dir /var/lib/redis/
logfile /var/log/redis.log
EOF
        print_success "Created Redis configuration"
    fi
    
    # Create data directory
    sudo mkdir -p /var/lib/redis
    sudo chown redis:redis /var/lib/redis
    
    sudo systemctl daemon-reload
    SERVICE_NAME="redis"
    print_success "Created redis.service"
fi

# Try to start and enable the service
print_status "Starting Redis service..."
if sudo systemctl start $SERVICE_NAME; then
    print_success "Redis service started"
    sudo systemctl enable $SERVICE_NAME
    print_success "Redis service enabled for startup"
else
    print_error "Failed to start Redis service"
    print_status "Checking service status..."
    sudo systemctl status $SERVICE_NAME --no-pager
fi

# Test Redis connection
print_status "Testing Redis connection..."
if redis-cli ping >/dev/null 2>&1; then
    print_success "Redis is responding to ping"
else
    print_warning "Redis is not responding. It may take a moment to start."
fi

echo ""
print_success "Redis fix completed!"
echo -e "${YELLOW}You can now re-run the Laravel setup script.${NC}"
