#!/bin/bash

# PHP Configuration Fix for Arch Linux
# This script fixes PHP extension loading issues

# Color codes
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[✓]${NC} $1"
}

print_error() {
    echo -e "${RED}[✗]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[!]${NC} $1"
}

echo -e "${BLUE}PHP Configuration Fix${NC}"
echo "===================="

# Find PHP configuration file
PHP_INI=$(php --ini 2>/dev/null | grep "Loaded Configuration File" | cut -d: -f2 | xargs)
if [[ -z "$PHP_INI" ]]; then
    PHP_INI="/etc/php/php.ini"
fi

print_status "PHP configuration file: $PHP_INI"

# Check if backup exists
if [[ ! -f "$PHP_INI.backup" ]]; then
    print_error "No backup found. Creating backup now..."
    sudo cp "$PHP_INI" "$PHP_INI.backup"
    print_success "Backup created: $PHP_INI.backup"
fi

# Core extensions that should NOT be loaded as extensions (they're built into PHP)
CORE_EXTENSIONS=(
    "ctype"
    "dom"
    "fileinfo"
    "json"
    "mbstring"
    "openssl"
    "tokenizer"
    "xml"
    "simplexml"
    "xmlreader"
    "zlib"
)

print_status "Removing incorrect core extension entries..."

# Remove core extensions from php.ini
for ext in "${CORE_EXTENSIONS[@]}"; do
    if grep -q "^extension=${ext}$" "$PHP_INI"; then
        print_step "Removing extension=${ext}"
        sudo sed -i "/^extension=${ext}$/d" "$PHP_INI"
        print_success "Removed extension=${ext}"
    fi
done

# Install missing packages for extensions that actually need to be loaded
print_status "Installing missing PHP extension packages..."

# Check and install missing packages
EXTENSION_PACKAGES=(
    "php-sodium"
)

for package in "${EXTENSION_PACKAGES[@]}"; do
    if ! pacman -Qi "$package" >/dev/null 2>&1; then
        print_step "Installing $package..."
        sudo pacman -S --noconfirm "$package"
        print_success "$package installed"
    else
        print_success "$package already installed"
    fi
done

# Fix Redis extension (needs igbinary)
print_status "Fixing Redis extension..."
if pacman -Qi php-redis >/dev/null 2>&1; then
    if ! pacman -Qi php-igbinary >/dev/null 2>&1; then
        print_step "Installing php-igbinary (required for Redis)..."
        sudo pacman -S --noconfirm php-igbinary
        print_success "php-igbinary installed"
    fi
    
    # Ensure igbinary is loaded before redis
    if ! grep -q "^extension=igbinary" "$PHP_INI"; then
        print_step "Adding igbinary extension..."
        # Add igbinary before redis in the file
        if grep -q "^extension=redis" "$PHP_INI"; then
            sudo sed -i '/^extension=redis/i extension=igbinary' "$PHP_INI"
        else
            echo "extension=igbinary" | sudo tee -a "$PHP_INI" > /dev/null
        fi
        print_success "Added igbinary extension"
    fi
fi

# Ensure only necessary extensions are loaded
print_status "Configuring necessary extensions..."

# Extensions that should be explicitly loaded (not core)
LOADABLE_EXTENSIONS=(
    "bcmath"
    "curl"
    "gd"
    "intl"
    "pdo_mysql"
    "pdo_pgsql"
    "pdo_sqlite"
    "pgsql"
    "sqlite3"
    "zip"
    "exif"
    "iconv"
)

# Function to ensure extension is properly configured
ensure_extension() {
    local ext=$1
    local ini_file=$2
    
    # Check if extension line exists and is uncommented
    if grep -q "^extension=${ext}" "$ini_file"; then
        print_success "Extension $ext is properly configured"
    elif grep -q "^;extension=${ext}" "$ini_file"; then
        print_step "Enabling extension: $ext"
        sudo sed -i "s/^;extension=${ext}/extension=${ext}/" "$ini_file"
        print_success "Extension $ext enabled"
    else
        # Check if this extension should be loaded (not a core extension)
        if [[ " ${LOADABLE_EXTENSIONS[@]} " =~ " ${ext} " ]]; then
            print_step "Adding extension: $ext"
            echo "extension=${ext}" | sudo tee -a "$ini_file" > /dev/null
            print_success "Extension $ext added"
        fi
    fi
}

# Configure loadable extensions
for ext in "${LOADABLE_EXTENSIONS[@]}"; do
    ensure_extension "$ext" "$PHP_INI"
done

# Add sodium extension if not present
if ! grep -q "^extension=sodium" "$PHP_INI" && ! grep -q "^;extension=sodium" "$PHP_INI"; then
    print_step "Adding sodium extension..."
    echo "extension=sodium" | sudo tee -a "$PHP_INI" > /dev/null
    print_success "Added sodium extension"
elif grep -q "^;extension=sodium" "$PHP_INI"; then
    print_step "Enabling sodium extension..."
    sudo sed -i "s/^;extension=sodium/extension=sodium/" "$PHP_INI"
    print_success "Enabled sodium extension"
fi

# Add sockets extension if not present
if ! grep -q "^extension=sockets" "$PHP_INI" && ! grep -q "^;extension=sockets" "$PHP_INI"; then
    print_step "Adding sockets extension..."
    echo "extension=sockets" | sudo tee -a "$PHP_INI" > /dev/null
    print_success "Added sockets extension"
elif grep -q "^;extension=sockets" "$PHP_INI"; then
    print_step "Enabling sockets extension..."
    sudo sed -i "s/^;extension=sockets/extension=sockets/" "$PHP_INI"
    print_success "Enabled sockets extension"
fi

print_status "Testing PHP configuration..."

# Test PHP configuration
if php -v >/dev/null 2>&1; then
    print_success "PHP configuration is valid"
else
    print_error "PHP configuration has errors"
    print_status "Restoring backup..."
    sudo cp "$PHP_INI.backup" "$PHP_INI"
    print_warning "Backup restored. Please check the configuration manually."
    exit 1
fi

# Show enabled extensions
print_status "Currently enabled PHP extensions:"
php -m | grep -E "(bcmath|curl|gd|intl|pdo|pgsql|sqlite|zip|sodium|sockets|redis|imagick)" | sort

print_success "PHP configuration fixed!"
echo ""
echo -e "${YELLOW}You can now run 'composer install' without extension errors.${NC}"
echo -e "${BLUE}If you still get warnings, they should not prevent Composer from working.${NC}"
